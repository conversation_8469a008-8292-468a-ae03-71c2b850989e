import type { BasicColumn, FormSchema } from '@/components/Table'
import { useRender } from '@/components/Table'
import { DICT_TYPE } from '@/utils/dict'
import { getDictOptions } from '@/utils/dict'
import { h } from 'vue'

export const columns: BasicColumn[] = [
  {
    title: '主键编号',
    dataIndex: 'id',
    width: 160,
    defaultHidden: true,
  },
  {
    title: '参数类型',
    dataIndex: 'confType',
    width: 160,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.CLUB_ARGUMENT_TYPE)
    },
  },
  {
    title: '配置项名称',
    dataIndex: 'confName',
    width: 160,
  },
  {
    title: '配置值',
    dataIndex: 'confVal',
    width: 300,
    customRender: ({ text, record }) => {
      try {
        const confType = record.confType;
        switch (confType) {
          case 'Switch':
            return text ? '开启' : '关闭';
          case 'FileUpload':
            if (!text) return '未设置';
            return h('img', {
              src: text,
              style: {
                height: '40px',
                width: 'auto',
                objectFit: 'cover',
                objectPosition: 'center',
              },
            });
          case 'Editor':
            return text ? '富文本内容(详情中查看)' : '未设置';
          case 'InputTextArea':
            return text || '未设置';
          default:
            return text || '未设置';
        }
      } catch {
        return text || '未设置';
      }
    }
  },
  {
    title: '配置描述',
    dataIndex: 'confDesc',
    width: 300,
    customRender: ({ text }) => {
      return text || '--';
    }
  },
  {
    title: '最后修改时间',
    dataIndex: 'updateTime',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text)
    }
  },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '配置项名称',
    field: 'confName',
    component: 'Input',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    colProps: { span: 8 }
  },
  {
    label: '配置类型',
    field: 'confType',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.CLUB_ARGUMENT_TYPE, 'string'),
    },
    colProps: { span: 8 }
  },
  {
    label: '最后修改时间',
    field: 'updateTime',
    component: 'RangePicker',
    colProps: { span: 8 }
  },
]


// 修改为函数形式，接收回调
export const getCreateFormSchema = (onChange?: (type: string) => void): FormSchema[] => [
  {
    label: '编号',
    field: 'id',
    show: false,
    component: 'Input'
  },
  {
    label: '配置项类型',
    field: 'confType',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.CLUB_ARGUMENT_TYPE, 'string'),
      placeholder: '请选择配置项类型',
      onChange: onChange || (() => { })
    },
    colProps: { span: 24 }
  },
  {
    label: '配置项名称',
    field: 'confName',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入配置名称'
    },
    colProps: { span: 24 }
  },
  {
    label: '配置key',
    field: 'confKey',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入配置key',
    },
    colProps: { span: 24 }
  },
  {
    label: '配置描述',
    field: 'confDesc',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入配置描述'
    },
    colProps: { span: 24 }
  },
]

export const getUpdateFormSchema = (onChange?: (type: string) => void): FormSchema[] => [
  {
    label: '编号',
    field: 'id',
    show: false,
    component: 'Input'
  },
  {
    label: '配置项类型',
    field: 'confType',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.CLUB_ARGUMENT_TYPE, 'string'),
      placeholder: '请选择配置项类型',
      disabled: true,
      onChange: onChange || (() => { })
    },
    colProps: { span: 24 }
  },
  {
    label: '配置项名称',
    field: 'confName',
    component: 'Input',
    required: true,
    componentProps: {
      disabled: true
    },
    colProps: { span: 24 }
  },
  {
    label: '配置描述',
    field: 'confDesc',
    component: 'InputTextArea',
    componentProps: {
      disabled: true
    },
    colProps: { span: 24 }
  },
]
